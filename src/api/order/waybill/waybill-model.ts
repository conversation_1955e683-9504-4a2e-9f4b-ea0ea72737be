export type WaybillPageResponse = {
    id: string;
    bizId: string;
    customerId: string;
    branchId: string;
    supplierId: string;
    orderNo: string;
    receiverName: string;
    country: string;
    state: string;
    city: string;
    street: string;
    street2: string | undefined;
    postcode: string;
    channel: string;
    channelId: string;
    shipMethod: string;
    shipMethodAlias: string;
    waybillNo: string;
    waybillLabelUrl: string;
    phone: string;
    errorMsg: string;
    status: string;
    failAtStatus: string;
    createdByName: string;
    createdAt: string;
    updatedByName: string;
    updatedAt: string;
    wayBillRelation: string;
    spu?: string;
    fileName?: string;
    orderNos: string;
    title?: string;
    quickChannelTax: number;
    customerNeedPayCost: number;
    customerNeedPayTax: number;
    calculateCustomerNeedPayCostFlow: string;
    product: ProductInfo;
    trackingNumber?: string;
    taxNumber?: string;
    hsCode?: string;
    material?: string;
    name?: string;
    totalPrice?: number;
    iossNumber?: string;
}


export type UpdateWaybillRequest = {
    receiverName?: string | undefined;
    country?: string | undefined;
    state?: string | undefined;
    city?: string | undefined;
    street?: string | undefined;
    postcode?: string | undefined;
    channel?: string | undefined;
    channelId?: string | undefined;
    waybillNo?: string | undefined;
    waybillLabelUrl?: string | undefined;
    phone?: string | undefined;
    taxNumber?: string | undefined;
    hsCode?: string | undefined;
    material?: string | undefined;
    street2?: string | undefined;
    name?: string | undefined;
    totalPrice?: number | undefined;
    weight?: number | undefined;
    iossNumber?: string | undefined;
}


export type FinanceStatisticResponse = {
    totalCustomerNeedPayCost: number;
    totalCustomerNeedPayTax: number;
    totalQuickChannelTax: number;
    totalWaybillCost: number;
}


export type WaybillTaxSetRequest = {
    waybillIds: string[];
    tax: number;
}

export interface StopPrintRequest {
    ids: string[]
    warning: string
}

export type CancelStopPrintRequest = Omit<StopPrintRequest, 'warning'>


export interface ProductInfo {
    spu: string;
    size: string;
    color: string;
    qty: number;
    spuId: string;
    skuId: string;
    weight: number;
    price: number;
    currency: string;
    name: string;
    cnName: string;
    title: string;
    supplierId: string;
    supplierName: string;
    detail: string;
    customName: string;
}
