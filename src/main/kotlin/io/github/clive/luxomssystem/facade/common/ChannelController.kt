package io.github.clive.luxomssystem.facade.common

import io.github.clive.luxomssystem.application.common.ChannelService
import io.github.clive.luxomssystem.facade.common.dto.ChannelResponse
import io.github.clive.luxomssystem.facade.common.dto.UpdateChannelRequest
import jakarta.validation.Valid
import org.springframework.web.bind.annotation.*

@RestController
@RequestMapping("/api/channels")
class ChannelController(
    private val channelService: ChannelService,
) {
    @GetMapping("/list")
    fun list() = channelService.list().map { ChannelResponse.fromDomain(it) }

    @GetMapping("/{id}")
    fun getById(@PathVariable id: Long): ChannelResponse {
        val channel = channelService.findById(id)
            ?: throw NoSuchElementException("Channel not found with id: $id")
        return ChannelResponse.fromDomain(channel)
    }

    @PutMapping("/{id}")
    fun updateChannel(
        @PathVariable id: Long,
        @Valid @RequestBody request: UpdateChannelRequest
    ): ChannelResponse {
        val existingChannel = channelService.findById(id)
            ?: throw NoSuchElementException("Channel not found with id: $id")
        val channelToUpdate = request.toDomain(id, existingChannel.name)
        val updatedChannel = channelService.updateChannel(id, channelToUpdate)
        return ChannelResponse.fromDomain(updatedChannel)
    }
}
