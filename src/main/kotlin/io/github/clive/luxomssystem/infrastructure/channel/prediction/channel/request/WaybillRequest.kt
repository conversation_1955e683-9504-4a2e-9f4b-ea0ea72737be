package io.github.clive.luxomssystem.infrastructure.channel.prediction.channel.request

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import io.github.clive.luxomssystem.domain.SubOrder
import io.github.clive.luxomssystem.domain.waybill.model.Waybill
import java.math.BigDecimal
import java.math.RoundingMode

data class WaybillRequest(
    var orderNo: String,
    var spu: String,
    var size: String,
    var color: String,
    var qty: Int = 1,
    var weight: BigDecimal = BigDecimal.ZERO,
    var price: BigDecimal = BigDecimal.ZERO,
    var currency: String = "",
    var name: String = "",
    var cnName: String = "",
    var title: String = "",
    var supplierName: String,
    var detail: String,
    var country: String = "",
    var material: String? = null,
    var hsCode: String? = null,
    var totalPrice: BigDecimal? = null,
    var channel: WaybillChannel? = null,
    var shipMethod: String? = null,
) {
    fun skuCode(): String = "$spu-$size-$color"

    private fun price(): BigDecimal = 5.toBigDecimal()


    fun price(
        multiSize: Int,
        country: String,
    ): BigDecimal {
        if (totalPrice != null) {
            return totalPrice!!.divide(multiSize.toBigDecimal(), 2, RoundingMode.HALF_UP)
        }
        throw IllegalArgumentException("totalPrice为null，无法计算单价")
    }

    companion object {
        fun of(
            order: SubOrder,
            waybill: Waybill,
        ): List<WaybillRequest> =
            if (order.product.onSet()) {
                buildList {
                    for (setSkuSize in order.product.setSizeList()) {
                        add(
                            WaybillRequest(
                                orderNo = order.orderNo!!,
                                spu = order.product.spu!!,
                                size = setSkuSize,
                                color = order.product.color!!,
                                qty = order.product.qty,
                                weight = waybill.product.weight,
                                price = order.product.price,
                                currency = order.product.currency,
                                name = waybill.product.name,
                                cnName = order.product.cnName,
                                title = order.product.title,
                                supplierName = order.product.supplierName ?: "",
                                detail = order.product.detail!!,
                                country = order.recipient.country ?: "",
                                material = waybill.product.material,
                                hsCode = waybill.product.hsCode,
                                totalPrice = waybill.totalPrice,
                                channel = waybill.shipping.channel,
                                shipMethod = waybill.shipping.shipMethod,
                            ),
                        )
                    }
                }
            } else {
                listOf(
                    WaybillRequest(
                        orderNo = order.orderNo!!,
                        spu = order.product.spu!!,
                        size = order.product.size!!,
                        color = order.product.color!!,
                        qty = order.product.qty,
                        weight = waybill.product.weight,
                        price = order.product.price,
                        currency = order.product.currency,
                        name = waybill.product.name,
                        cnName = order.product.cnName,
                        title = order.product.title,
                        supplierName = order.product.supplierName ?: "",
                        detail = order.product.detail ?: "",
                        country = order.recipient.country ?: "",
                        material = waybill.product.material,
                        hsCode = waybill.product.hsCode,
                        totalPrice = waybill.totalPrice,
                        channel = waybill.shipping.channel,
                        shipMethod = waybill.shipping.shipMethod,
                    ),
                )
            }
    }
}

fun totalPrice(country: String, multiSize: Int, channel: WaybillChannel): BigDecimal {
    if ("NL".equals(country, true)) {
        return BigDecimal.valueOf(3)
    }
    if (channel in listOf(
            WaybillChannel.YW_HZ,
            WaybillChannel.YW_QZ,
            WaybillChannel.YW_GZ,
            WaybillChannel.YW_YW,
            WaybillChannel.YUNTU
        )
    ) {
        val price =
            if (multiSize <= 10) {
                BigDecimal.valueOf(1)
            } else if (multiSize <= 20) {
                BigDecimal.valueOf(5)
            } else if (multiSize <= 40) {
                BigDecimal.valueOf(10)
            } else {
                throw IllegalArgumentException("总件数不能超过40")
            }
        return price
    }

    if (multiSize > 40) {
        throw IllegalArgumentException("总金额不能超过30美金")
    }

    return BigDecimal.valueOf(5)
}
