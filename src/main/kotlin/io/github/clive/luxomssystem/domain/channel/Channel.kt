package io.github.clive.luxomssystem.domain.channel

import io.github.clive.luxomssystem.common.enums.WaybillChannel
import jakarta.persistence.*

@Entity(name = "channels")
@Table(name = "channels")
class Channel {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    var id: Long = 0

    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    var name: WaybillChannel = WaybillChannel.SF

    @Column(name = "display_name")
    var displayName: String = name.displayName

    @Column(nullable = false)
    var methodCode: String = ""

    var methodName: String = ""

    var enabled: Boolean = true
}
